{"name": "backend", "version": "1.0.0", "description": "Peptide order portal backend API", "main": "dist/src/index.js", "type": "module", "scripts": {"dev": "NODE_ENV=development tsx watch src/index.ts", "dev:prod": "NODE_ENV=production tsx watch src/index.ts", "build": "prisma generate && rm -rf dist && tsc && ls -la dist/", "start": "ls -la dist/ && NODE_ENV=production node dist/src/index.js", "start:dev": "NODE_ENV=development node dist/src/index.js", "typecheck": "tsc --noEmit", "db:push": "NODE_ENV=development prisma db push", "db:push:prod": "NODE_ENV=production prisma db push", "db:seed": "NODE_ENV=development tsx src/scripts/seed.ts", "db:seed:prod": "NODE_ENV=production tsx src/scripts/seed.ts", "db:delete-orders": "NODE_ENV=development tsx src/scripts/delete-orders.ts", "db:migrate-status": "tsx src/scripts/migrate-order-status.ts", "test:calc": "tsx src/scripts/test-calculations.ts", "test:api": "tsx src/scripts/test-api.ts", "test:status": "tsx src/scripts/test-order-status.ts", "test:parcels": "tsx src/scripts/test-parcels-api.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^11.0.1", "@fastify/env": "^5.0.2", "@prisma/client": "^6.11.1", "@tsconfig/node20": "^20.1.6", "@types/cron": "^2.0.1", "@types/nodemailer": "^6.4.17", "axios": "^1.10.0", "cron": "^4.3.2", "csv-parse": "^6.1.0", "dotenv-flow": "^4.1.0", "envalid": "^8.1.0", "exceljs": "^4.4.0", "fastify": "^5.4.0", "nodemailer": "^7.0.5", "pdf-lib": "^1.17.1", "prisma": "^6.11.1"}, "devDependencies": {"@types/node": "^24.0.12", "pino-pretty": "^13.0.0", "rimraf": "^6.0.1", "tsx": "^4.20.3", "typescript": "^5.8.3"}}