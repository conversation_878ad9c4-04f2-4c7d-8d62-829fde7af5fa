import { promises as fs } from 'fs';
import path from 'path';

async function cleanCSV() {
  console.log('🧹 Cleaning CSV file...');
  
  const csvPath = path.join(process.cwd(), '..', 'backup-data', 'lost-orders.csv');
  const content = await fs.readFile(csvPath, 'utf-8');
  
  const lines = content.split('\n');
  const cleanedLines: string[] = [];
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    if (!line) continue;
    
    if (i === 0) {
      // Header line - keep as is
      cleanedLines.push(line);
    } else {
      // Data line - need to properly quote the date field
      // The date field is the second field and contains commas
      const parts = line.split(',');
      
      // Reconstruct the line with quoted date field
      if (parts.length >= 17) {
        // Find where the date field ends by looking for the pattern
        // The date should end with "(Asia/Shanghai)"
        let dateEndIndex = -1;
        let reconstructedDate = '';
        
        for (let j = 1; j < parts.length; j++) {
          if (j === 1) {
            reconstructedDate = parts[j];
          } else {
            reconstructedDate += ',' + parts[j];
          }
          
          if (parts[j].includes('(Asia/Shanghai)')) {
            dateEndIndex = j;
            break;
          }
        }
        
        if (dateEndIndex > 1) {
          // Reconstruct the line with properly quoted date
          const newParts = [
            parts[0], // Order ID
            `"${reconstructedDate}"`, // Quoted date
            ...parts.slice(dateEndIndex + 1) // Rest of the fields
          ];
          cleanedLines.push(newParts.join(','));
        } else {
          // Fallback - just quote the second field
          parts[1] = `"${parts[1]}"`;
          cleanedLines.push(parts.join(','));
        }
      } else {
        cleanedLines.push(line);
      }
    }
  }
  
  const cleanedContent = cleanedLines.join('\n');
  const cleanedPath = path.join(process.cwd(), '..', 'backup-data', 'lost-orders-cleaned.csv');
  await fs.writeFile(cleanedPath, cleanedContent);
  
  console.log(`✅ Cleaned CSV saved to: ${cleanedPath}`);
  console.log(`Original lines: ${lines.length}`);
  console.log(`Cleaned lines: ${cleanedLines.length}`);
}

if (import.meta.url === `file://${process.argv[1]}`) {
  cleanCSV().catch(console.error);
}
