import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function markOrdersAsSent() {
  console.log('📦 Marking lost orders as sent...\n');
  
  const orderNumbers = [
    'ORD-2025-0089', 'ORD-2025-0090', 'ORD-2025-0091', 'ORD-2025-0092', 
    'ORD-2025-0093', 'ORD-2025-0094', 'ORD-2025-0095', 'ORD-2025-0096', 
    'ORD-2025-0097', 'ORD-2025-0098', 'ORD-2025-0099', 'ORD-2025-0100', 
    'ORD-2025-0101', 'ORD-2025-0102'
  ];
  
  // Set the sent date to August 5, 2025 10:36 PM
  const sentDate = new Date('2025-08-05T22:36:00.000Z');
  
  console.log(`📅 Sent date: ${sentDate.toLocaleString()}`);
  console.log(`🔍 Finding orders to update...\n`);
  
  // Find the orders first
  const orders = await prisma.order.findMany({
    where: {
      orderNumber: { in: orderNumbers }
    },
    select: {
      id: true,
      orderNumber: true,
      customerName: true,
      sentToSupplier: true,
      sentAt: true,
      status: true
    },
    orderBy: {
      orderNumber: 'asc'
    }
  });
  
  if (orders.length === 0) {
    console.log('❌ No orders found to update');
    return;
  }
  
  console.log(`📋 Found ${orders.length} orders to mark as sent:`);
  orders.forEach(order => {
    const currentStatus = order.sentToSupplier ? 'Already sent' : 'Not sent';
    console.log(`  - ${order.orderNumber} (${order.customerName}) - ${currentStatus}`);
  });
  
  console.log('\n🚀 Updating orders...\n');
  
  let updatedCount = 0;
  let skippedCount = 0;
  
  for (const order of orders) {
    try {
      if (order.sentToSupplier) {
        console.log(`  ⏭️  ${order.orderNumber} - Already marked as sent, skipping`);
        skippedCount++;
        continue;
      }
      
      await prisma.order.update({
        where: { id: order.id },
        data: {
          sentToSupplier: true,
          sentAt: sentDate,
          status: 'sent'
        }
      });
      
      console.log(`  ✅ ${order.orderNumber} - Marked as sent`);
      updatedCount++;
      
    } catch (error) {
      console.log(`  ❌ ${order.orderNumber} - Failed to update: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  
  console.log('\n📊 UPDATE SUMMARY');
  console.log('='.repeat(50));
  console.log(`Orders updated: ${updatedCount}`);
  console.log(`Orders skipped: ${skippedCount}`);
  console.log(`Total orders: ${orders.length}`);
  console.log(`Sent date: ${sentDate.toLocaleString()}`);
  
  if (updatedCount > 0) {
    console.log('\n✅ Orders successfully marked as sent!');
    console.log('\nThese orders will now show as:');
    console.log('  - Status: "sent"');
    console.log('  - Supplier Status: "sent"');
    console.log(`  - Sent Date: ${sentDate.toLocaleDateString()}`);
  }
  
  await prisma.$disconnect();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  markOrdersAsSent().catch(console.error);
}

export { markOrdersAsSent };
