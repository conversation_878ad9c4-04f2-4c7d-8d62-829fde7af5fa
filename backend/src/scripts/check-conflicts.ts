import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkConflicts() {
  const orderNumbers = [
    'ORD-2025-0089', 'ORD-2025-0090', 'ORD-2025-0091', 'ORD-2025-0092', 
    'ORD-2025-0093', 'ORD-2025-0094', 'ORD-2025-0095', 'ORD-2025-0096', 
    'ORD-2025-0097', 'ORD-2025-0098', 'ORD-2025-0099', 'ORD-2025-0100', 
    'ORD-2025-0101', 'ORD-2025-0102'
  ];

  const existingOrders = await prisma.order.findMany({
    where: {
      orderNumber: { in: orderNumbers }
    },
    select: {
      orderNumber: true,
      customerName: true
    }
  });

  if (existingOrders.length > 0) {
    console.log('❌ Found existing orders that would conflict:');
    existingOrders.forEach(order => {
      console.log(`  - ${order.orderNumber} (${order.customerName})`);
    });
  } else {
    console.log('✅ No conflicting orders found in database');
  }

  await prisma.$disconnect();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  checkConflicts();
}
