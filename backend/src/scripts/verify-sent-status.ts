import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function verifySentStatus() {
  const orderNumbers = [
    'ORD-2025-0089', 'ORD-2025-0090', 'ORD-2025-0091', 'ORD-2025-0092', 
    'ORD-2025-0093', 'ORD-2025-0094', 'ORD-2025-0095', 'ORD-2025-0096', 
    'ORD-2025-0097', 'ORD-2025-0098', 'ORD-2025-0099', 'ORD-2025-0100', 
    'ORD-2025-0101', 'ORD-2025-0102'
  ];

  const orders = await prisma.order.findMany({
    where: {
      orderNumber: { in: orderNumbers }
    },
    select: {
      orderNumber: true,
      customerName: true,
      status: true,
      sentToSupplier: true,
      sentAt: true
    },
    orderBy: {
      orderNumber: 'asc'
    }
  });

  console.log('📋 Verification of sent status:\n');

  orders.forEach(order => {
    const sentDate = order.sentAt ? order.sentAt.toLocaleDateString() : 'Not set';
    const supplierStatus = order.sentToSupplier ? '✅ Sent' : '❌ Not sent';
    
    console.log(`📦 ${order.orderNumber} - ${order.customerName}`);
    console.log(`   Status: ${order.status}`);
    console.log(`   Supplier Status: ${supplierStatus}`);
    console.log(`   Sent Date: ${sentDate}`);
    console.log();
  });

  const sentCount = orders.filter(o => o.sentToSupplier).length;
  console.log(`📊 Summary: ${sentCount}/${orders.length} orders marked as sent`);

  await prisma.$disconnect();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  verifySentStatus();
}
