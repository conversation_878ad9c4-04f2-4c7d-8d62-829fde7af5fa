import { promises as fs } from 'fs';
import path from 'path';

async function debugOrder97() {
  const csvPath = path.join(process.cwd(), '..', 'backup-data', 'lost-orders.csv');
  const csvContent = await fs.readFile(csvPath, 'utf-8');
  const lines = csvContent.split('\n').filter(line => line.trim());

  // Find ORD-2025-0097 lines
  const order97Lines = lines.filter(line => line.startsWith('ORD-2025-0097'));
  console.log('ORD-2025-0097 lines:');
  order97Lines.forEach((line, i) => {
    console.log(`Line ${i + 1}: ${line}`);
    const parts = line.split(',');
    console.log(`  Split into ${parts.length} parts`);
    console.log(`  Parts 0-8: ${parts.slice(0, 9).join(' | ')}`);
    console.log(`  Last 9 parts: ${parts.slice(-9).join(' | ')}`);
    
    // Simulate the parsing logic
    const totalFields = parts.slice(-9);
    const frontFields = parts.slice(0, parts.length - 9);
    
    console.log(`  Front fields: ${frontFields.join(' | ')}`);
    
    const addressFields = frontFields.slice(4);
    console.log(`  Address fields: ${addressFields.join(' | ')}`);
    
    if (addressFields.length >= 4) {
      const country = addressFields[addressFields.length - 1];
      const zip = addressFields[addressFields.length - 2];

      let address, city, state;

      if (addressFields.length === 4) {
        // Exactly 4 fields: address, city, zip, country
        address = addressFields[0];
        city = addressFields[1];
        state = ''; // No state for international
      } else {
        // More than 4 fields: some combination of address parts, city, state, zip, country
        state = addressFields[addressFields.length - 3];
        city = addressFields[addressFields.length - 4];

        // Everything else is address
        const addressParts = addressFields.slice(0, addressFields.length - 4);
        address = addressParts.join(',');
      }

      console.log(`  Parsed address: "${address}"`);
      console.log(`  Parsed city: "${city}"`);
      console.log(`  Parsed state: "${state}"`);
      console.log(`  Parsed zip: "${zip}"`);
      console.log(`  Parsed country: "${country}"`);
    }
    
    console.log();
  });
}

if (import.meta.url === `file://${process.argv[1]}`) {
  debugOrder97();
}
