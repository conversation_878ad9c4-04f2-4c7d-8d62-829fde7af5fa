import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function verifyImport() {
  const orderNumbers = [
    'ORD-2025-0089', 'ORD-2025-0090', 'ORD-2025-0091', 'ORD-2025-0092', 
    'ORD-2025-0093', 'ORD-2025-0094', 'ORD-2025-0095', 'ORD-2025-0096', 
    'ORD-2025-0097', 'ORD-2025-0098', 'ORD-2025-0099', 'ORD-2025-0100', 
    'ORD-2025-0101', 'ORD-2025-0102'
  ];

  const orders = await prisma.order.findMany({
    where: {
      orderNumber: { in: orderNumbers }
    },
    include: {
      items: true
    },
    orderBy: {
      orderNumber: 'asc'
    }
  });

  console.log(`✅ Successfully imported ${orders.length} orders:`);
  console.log();

  let totalValue = 0;
  let totalItems = 0;

  orders.forEach(order => {
    const itemCount = order.items.length;
    const orderTotal = Number(order.totalUsd);
    totalValue += orderTotal;
    totalItems += itemCount;
    
    console.log(`📦 ${order.orderNumber} - ${order.customerName}`);
    console.log(`   📍 ${order.city}, ${order.state || ''} ${order.country}`);
    console.log(`   🛒 ${itemCount} items - $${orderTotal.toFixed(2)}`);
    console.log(`   📅 ${order.placedAt.toISOString().split('T')[0]}`);
    console.log(`   🆔 ID: ${order.id}`);
    console.log();
  });

  console.log(`📊 IMPORT TOTALS:`);
  console.log(`   Orders: ${orders.length}`);
  console.log(`   Items: ${totalItems}`);
  console.log(`   Total Value: $${totalValue.toFixed(2)}`);

  // Create rollback log manually
  const orderIds = orders.map(o => o.id);
  const orderNums = orders.map(o => o.orderNumber);
  
  console.log('\n💾 Creating rollback log...');
  
  try {
    const { saveRollbackLog } = await import('./rollback-lost-orders.js');
    await saveRollbackLog(orderIds, orderNums);
    console.log('✅ Rollback log created successfully');
  } catch (error) {
    console.log('⚠️  Could not create rollback log:', error);
    console.log('\n📝 Manual rollback info:');
    console.log(`Order IDs: ${orderIds.join(', ')}`);
  }

  await prisma.$disconnect();
}

if (import.meta.url === `file://${process.argv[1]}`) {
  verifyImport();
}
