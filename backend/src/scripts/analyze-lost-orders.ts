import { promises as fs } from 'fs';
import path from 'path';
import { parse } from 'csv-parse/sync';

interface CSVRow {
  'Order ID': string;
  'Date': string;
  'Customer Name': string;
  'Customer Address': string;
  'Customer City': string;
  'Customer State': string;
  'Customer Zip Code': string;
  'Customer Country': string;
  'Item Name': string;
  'Dose': string;
  'Qty': string;
  'Price': string;
  'Total for Item': string;
  'Subtotal': string;
  'Shipping': string;
  'Discount (5%)': string;
  'Total (final)': string;
}

interface OrderItem {
  code: string;
  dose: string;
  qty: number;
  price: number;
  totalForItem: number;
}

interface GroupedOrder {
  orderNumber: string;
  date: string;
  customerName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  items: OrderItem[];
  subtotal: number;
  shipping: number;
  discount: number;
  total: number;
}

function parsePrice(priceStr: string | undefined): number {
  if (!priceStr) return 0;
  // Remove $ and - signs, convert to number
  return parseFloat(priceStr.replace(/[$,-]/g, ''));
}

function parseDate(dateStr: string): Date {
  // Parse "July 31, 2025 at 02:30 AM (Asia/Shanghai)" format
  const match = dateStr.match(/^(\w+ \d+, \d+) at (\d+:\d+ [AP]M)/);
  if (!match) {
    throw new Error(`Invalid date format: ${dateStr}`);
  }
  
  const [, datePart, timePart] = match;
  return new Date(`${datePart} ${timePart}`);
}

async function analyzeLostOrders() {
  console.log('🔍 Analyzing Lost Orders CSV Data\n');

  try {
    // Read original CSV file and parse manually
    const csvPath = path.join(process.cwd(), '..', 'backup-data', 'lost-orders.csv');
    const csvContent = await fs.readFile(csvPath, 'utf-8');
    const lines = csvContent.split('\n').filter(line => line.trim());

    // Parse header
    const header = lines[0].split(',');
    console.log('Header:', header);

    // Parse data lines manually since they have complex comma issues
    const records: CSVRow[] = [];

    for (let i = 1; i < lines.length; i++) {
      const line = lines[i];
      // Split by comma but be smart about it
      // We know the last 9 fields are: Item,Dose,Qty,Price,ItemTotal,Subtotal,Shipping,Discount,Total
      // And they don't contain commas, so we can work backwards

      const parts = line.split(',');

      if (parts.length >= 17) {
        const record: any = {};

        // Work backwards from the end - last 9 fields are clean
        const totalFields = parts.slice(-9);
        const frontFields = parts.slice(0, parts.length - 9);



        record['Order ID'] = frontFields[0];
        record['Date'] = frontFields[1] + ', ' + frontFields[2]; // Rejoin date parts
        record['Customer Name'] = frontFields[3];

        // The address, city, state, zip, country might have commas
        // We know country is the last field before the item fields
        // So we work backwards: country, zip, state, city, then everything else is address
        const addressFields = frontFields.slice(4); // Everything after name

        if (addressFields.length >= 4) {
          record['Customer Country'] = addressFields[addressFields.length - 1];
          record['Customer Zip Code'] = addressFields[addressFields.length - 2];
          record['Customer State'] = addressFields[addressFields.length - 3];
          record['Customer City'] = addressFields[addressFields.length - 4];

          // Everything else is address
          const addressParts = addressFields.slice(0, addressFields.length - 4);
          record['Customer Address'] = addressParts.join(',');
        }

        // Assign the clean fields
        record['Item Name'] = totalFields[0];
        record['Dose'] = totalFields[1];
        record['Qty'] = totalFields[2];
        record['Price'] = totalFields[3];
        record['Total for Item'] = totalFields[4];
        record['Subtotal'] = totalFields[5];
        record['Shipping'] = totalFields[6];
        record['Discount (5%)'] = totalFields[7];
        record['Total (final)'] = totalFields[8];


        records.push(record as CSVRow);
      }
    }
    
    console.log(`📊 Total CSV rows: ${records.length}`);
    
    // Group by Order ID
    const orderGroups = new Map<string, CSVRow[]>();
    
    for (const record of records) {
      const orderId = record['Order ID'];
      if (!orderGroups.has(orderId)) {
        orderGroups.set(orderId, []);
      }
      orderGroups.get(orderId)!.push(record);
    }
    
    console.log(`📦 Unique orders found: ${orderGroups.size}`);
    console.log(`📋 Order numbers: ${Array.from(orderGroups.keys()).sort().join(', ')}\n`);
    
    // Analyze each order
    const groupedOrders: GroupedOrder[] = [];
    const issues: string[] = [];
    
    for (const [orderNumber, rows] of orderGroups) {
      console.log(`\n🔍 Analyzing ${orderNumber}:`);
      
      // Validate all rows have same customer info
      const firstRow = rows[0];
      const customerInfo = {
        name: firstRow['Customer Name'],
        address: firstRow['Customer Address'],
        city: firstRow['Customer City'],
        state: firstRow['Customer State'],
        zipCode: firstRow['Customer Zip Code'],
        country: firstRow['Customer Country'],
        date: firstRow['Date'],
        subtotal: parsePrice(firstRow['Subtotal']),
        shipping: parsePrice(firstRow['Shipping']),
        discount: Math.abs(parsePrice(firstRow['Discount (5%)'])), // Remove negative sign
        total: parsePrice(firstRow['Total (final)'])
      };
      
      // Check consistency across rows
      for (const row of rows) {
        if (row['Customer Name'] !== customerInfo.name ||
            row['Customer Address'] !== customerInfo.address ||
            row['Date'] !== customerInfo.date) {
          issues.push(`❌ ${orderNumber}: Inconsistent customer data across items`);
        }
        
        if (parsePrice(row['Subtotal']) !== customerInfo.subtotal ||
            parsePrice(row['Shipping']) !== customerInfo.shipping ||
            parsePrice(row['Total (final)']) !== customerInfo.total) {
          issues.push(`❌ ${orderNumber}: Inconsistent totals across items`);
        }
      }
      
      // Parse items
      const items: OrderItem[] = rows.map((row, index) => {
        return {
          code: row['Item Name'],
          dose: row['Dose'],
          qty: parseInt(row['Qty']),
          price: parsePrice(row['Price']),
          totalForItem: parsePrice(row['Total for Item'])
        };
      });
      
      // Validate item calculations
      for (const item of items) {
        const expectedTotal = item.price * item.qty;
        if (Math.abs(expectedTotal - item.totalForItem) > 0.01) {
          issues.push(`❌ ${orderNumber}: Item ${item.code} calculation mismatch. Expected: $${expectedTotal}, Got: $${item.totalForItem}`);
        }
      }
      
      // Validate order totals
      const calculatedSubtotal = items.reduce((sum, item) => sum + item.totalForItem, 0);
      if (Math.abs(calculatedSubtotal - customerInfo.subtotal) > 0.01) {
        issues.push(`❌ ${orderNumber}: Subtotal mismatch. Calculated: $${calculatedSubtotal}, CSV: $${customerInfo.subtotal}`);
      }
      
      // Validate discount calculation (should be 5% for orders 89+)
      const expectedDiscount = customerInfo.subtotal * 0.05;
      if (Math.abs(expectedDiscount - customerInfo.discount) > 0.01) {
        issues.push(`❌ ${orderNumber}: Discount mismatch. Expected: $${expectedDiscount}, CSV: $${customerInfo.discount}`);
      }
      
      // Validate final total
      const expectedTotal = customerInfo.subtotal + customerInfo.shipping - customerInfo.discount;
      if (Math.abs(expectedTotal - customerInfo.total) > 0.01) {
        issues.push(`❌ ${orderNumber}: Final total mismatch. Expected: $${expectedTotal}, CSV: $${customerInfo.total}`);
      }
      
      console.log(`  👤 Customer: ${customerInfo.name}`);
      console.log(`  📍 Location: ${customerInfo.city}, ${customerInfo.state}, ${customerInfo.country}`);
      console.log(`  📅 Date: ${customerInfo.date}`);
      console.log(`  🛒 Items: ${items.length}`);
      items.forEach(item => {
        console.log(`    - ${item.code} ${item.dose} x${item.qty} @ $${item.price} = $${item.totalForItem}`);
      });
      console.log(`  💰 Subtotal: $${customerInfo.subtotal}`);
      console.log(`  🚚 Shipping: $${customerInfo.shipping}`);
      console.log(`  💸 Discount: $${customerInfo.discount}`);
      console.log(`  💵 Total: $${customerInfo.total}`);
      
      groupedOrders.push({
        orderNumber,
        date: customerInfo.date,
        customerName: customerInfo.name,
        address: customerInfo.address,
        city: customerInfo.city,
        state: customerInfo.state,
        zipCode: customerInfo.zipCode,
        country: customerInfo.country,
        items,
        subtotal: customerInfo.subtotal,
        shipping: customerInfo.shipping,
        discount: customerInfo.discount,
        total: customerInfo.total
      });
    }
    
    // Summary
    console.log('\n📊 ANALYSIS SUMMARY');
    console.log('='.repeat(50));
    console.log(`Total orders to restore: ${groupedOrders.length}`);
    console.log(`Total items across all orders: ${groupedOrders.reduce((sum, order) => sum + order.items.length, 0)}`);
    console.log(`Date range: ${groupedOrders[0]?.date} to ${groupedOrders[groupedOrders.length - 1]?.date}`);
    console.log(`Total value: $${groupedOrders.reduce((sum, order) => sum + order.total, 0).toFixed(2)}`);
    
    // Unique product codes
    const allCodes = new Set<string>();
    groupedOrders.forEach(order => {
      order.items.forEach(item => allCodes.add(`${item.code}-${item.dose}`));
    });
    console.log(`\nUnique product variants: ${allCodes.size}`);
    console.log('Product codes found:');
    Array.from(allCodes).sort().forEach(code => console.log(`  - ${code}`));
    
    // Issues found
    if (issues.length > 0) {
      console.log('\n⚠️  ISSUES FOUND:');
      issues.forEach(issue => console.log(issue));
    } else {
      console.log('\n✅ No data integrity issues found!');
    }
    
    // Save analysis results
    const analysisResult = {
      totalOrders: groupedOrders.length,
      totalItems: groupedOrders.reduce((sum, order) => sum + order.items.length, 0),
      totalValue: groupedOrders.reduce((sum, order) => sum + order.total, 0),
      uniqueProductVariants: Array.from(allCodes).sort(),
      orders: groupedOrders,
      issues
    };
    
    const outputPath = path.join(process.cwd(), 'src', 'scripts', 'lost-orders-analysis.json');
    await fs.writeFile(outputPath, JSON.stringify(analysisResult, null, 2));
    console.log(`\n💾 Analysis saved to: ${outputPath}`);
    
    return analysisResult;
    
  } catch (error) {
    console.error('❌ Error analyzing lost orders:', error);
    throw error;
  }
}

// Run analysis if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  analyzeLostOrders()
    .then(() => {
      console.log('\n✅ Analysis complete!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Analysis failed:', error);
      process.exit(1);
    });
}

export { analyzeLostOrders };
