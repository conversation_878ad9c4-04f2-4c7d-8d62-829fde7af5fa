import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

async function checkVariants() {
  try {
    const variants = await prisma.productVariant.findMany({
      include: { product: true },
      orderBy: { code: 'asc' }
    });
    
    console.log('Available product variants:');
    variants.forEach(v => {
      console.log(`  ${v.code} - ${v.dose} (${v.product.name}) - Buy: $${v.buyingPrice}, Sell: $${v.sellingPrice}`);
    });
    
    // Check which codes from CSV are missing
    const csvCodes = ['BA10', 'CGL5', 'KLOW80', 'MS10', 'RT10', 'RT12', 'RT30', 'RT5', 'RT50', 'SM10', 'TR10', 'TR20', 'TR30', 'TR5', 'TR60', 'TSM10'];
    const dbCodes = variants.map(v => v.code);
    const missing = csvCodes.filter(code => !dbCodes.includes(code));
    
    if (missing.length > 0) {
      console.log('\n❌ Missing product codes in database:');
      missing.forEach(code => console.log(`  - ${code}`));
    } else {
      console.log('\n✅ All CSV product codes found in database!');
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  checkVariants();
}
