import { promises as fs } from 'fs';
import path from 'path';
import { PrismaClient } from '../generated/prisma/index.js';
import type { CreateOrderRequest } from '../../shared/types/order.types.js';
import { saveRollbackLog } from './rollback-lost-orders.js';

const prisma = new PrismaClient();

interface CSVRow {
  'Order ID': string;
  'Date': string;
  'Customer Name': string;
  'Customer Address': string;
  'Customer City': string;
  'Customer State': string;
  'Customer Zip Code': string;
  'Customer Country': string;
  'Item Name': string;
  'Dose': string;
  'Qty': string;
  'Price': string;
  'Total for Item': string;
  'Subtotal': string;
  'Shipping': string;
  'Discount (5%)': string;
  'Total (final)': string;
}

interface OrderItem {
  code: string;
  dose: string;
  qty: number;
}

interface GroupedOrder {
  orderNumber: string;
  date: string;
  customerName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  items: OrderItem[];
}

interface ImportResult {
  success: boolean;
  ordersProcessed: number;
  ordersCreated: number;
  ordersSkipped: number;
  errors: string[];
  createdOrderIds: string[];
}

function parsePrice(priceStr: string | undefined): number {
  if (!priceStr) return 0;
  return parseFloat(priceStr.replace(/[$,-]/g, ''));
}

function parseDate(dateStr: string): Date {
  // Parse "July 31,  2025 at 02:30 AM (Asia/Shanghai)" format
  const cleanDate = dateStr.replace(/,\s+/, ', '); // Normalize spacing
  const match = cleanDate.match(/^(\w+ \d+, \d+) at (\d+:\d+ [AP]M)/);
  if (!match) {
    throw new Error(`Invalid date format: ${dateStr}`);
  }
  
  const [, datePart, timePart] = match;
  return new Date(`${datePart} ${timePart}`);
}

async function parseCSV(): Promise<GroupedOrder[]> {
  console.log('📄 Parsing CSV file...');
  
  const csvPath = path.join(process.cwd(), '..', 'backup-data', 'lost-orders.csv');
  const csvContent = await fs.readFile(csvPath, 'utf-8');
  const lines = csvContent.split('\n').filter(line => line.trim());
  
  // Parse data lines manually since they have complex comma issues
  const records: CSVRow[] = [];
  
  for (let i = 1; i < lines.length; i++) {
    const line = lines[i];
    const parts = line.split(',');
    
    if (parts.length >= 17) {
      const record: any = {};
      
      // Work backwards from the end - last 9 fields are clean
      const totalFields = parts.slice(-9);
      const frontFields = parts.slice(0, parts.length - 9);
      
      record['Order ID'] = frontFields[0];
      record['Date'] = frontFields[1] + ', ' + frontFields[2]; // Rejoin date parts
      record['Customer Name'] = frontFields[3];
      
      // The address, city, state, zip, country might have commas
      const addressFields = frontFields.slice(4);
      
      if (addressFields.length >= 4) {
        record['Customer Country'] = addressFields[addressFields.length - 1];
        record['Customer Zip Code'] = addressFields[addressFields.length - 2];

        // For international addresses, state might be empty or part of city
        if (addressFields.length === 4) {
          // Exactly 4 fields: address, city, zip, country
          record['Customer Address'] = addressFields[0];
          record['Customer City'] = addressFields[1];
          record['Customer State'] = ''; // No state for international
        } else {
          // More than 4 fields: some combination of address parts, city, state, zip, country
          record['Customer State'] = addressFields[addressFields.length - 3];
          record['Customer City'] = addressFields[addressFields.length - 4];

          // Everything else is address
          const addressParts = addressFields.slice(0, addressFields.length - 4);
          record['Customer Address'] = addressParts.join(',');
        }
      }
      
      // Assign the clean fields
      record['Item Name'] = totalFields[0];
      record['Dose'] = totalFields[1];
      record['Qty'] = totalFields[2];
      record['Price'] = totalFields[3];
      record['Total for Item'] = totalFields[4];
      record['Subtotal'] = totalFields[5];
      record['Shipping'] = totalFields[6];
      record['Discount (5%)'] = totalFields[7];
      record['Total (final)'] = totalFields[8];
      
      records.push(record as CSVRow);
    }
  }
  
  console.log(`📊 Parsed ${records.length} CSV rows`);
  
  // Group by Order ID
  const orderGroups = new Map<string, CSVRow[]>();
  
  for (const record of records) {
    const orderId = record['Order ID'];
    if (!orderGroups.has(orderId)) {
      orderGroups.set(orderId, []);
    }
    orderGroups.get(orderId)!.push(record);
  }
  
  console.log(`📦 Found ${orderGroups.size} unique orders`);
  
  // Convert to grouped orders
  const groupedOrders: GroupedOrder[] = [];
  
  for (const [orderNumber, rows] of orderGroups) {
    const firstRow = rows[0];
    
    const items: OrderItem[] = rows.map(row => ({
      code: row['Item Name'],
      dose: row['Dose'],
      qty: parseInt(row['Qty'])
    }));
    
    groupedOrders.push({
      orderNumber,
      date: firstRow['Date'],
      customerName: firstRow['Customer Name'],
      address: firstRow['Customer Address'],
      city: firstRow['Customer City'],
      state: firstRow['Customer State'],
      zipCode: firstRow['Customer Zip Code'],
      country: firstRow['Customer Country'],
      items
    });
  }
  
  return groupedOrders.sort((a, b) => a.orderNumber.localeCompare(b.orderNumber));
}

async function validateOrder(order: GroupedOrder): Promise<string[]> {
  const errors: string[] = [];

  // Check if order already exists
  const existingOrder = await prisma.order.findUnique({
    where: { orderNumber: order.orderNumber }
  });

  if (existingOrder) {
    errors.push(`Order ${order.orderNumber} already exists in database`);
  }

  // Validate order number format
  if (!/^ORD-\d{4}-\d{4}$/.test(order.orderNumber)) {
    errors.push(`Order ${order.orderNumber}: Invalid order number format`);
  }

  // Validate required customer fields
  if (!order.customerName.trim()) {
    errors.push(`Order ${order.orderNumber}: Customer name is required`);
  } else if (order.customerName.length > 100) {
    errors.push(`Order ${order.orderNumber}: Customer name too long (max 100 characters)`);
  }

  if (!order.address.trim()) {
    errors.push(`Order ${order.orderNumber}: Customer address is required`);
  } else if (order.address.length > 100) {
    errors.push(`Order ${order.orderNumber}: Customer address too long (max 100 characters)`);
  }

  if (!order.city.trim()) {
    errors.push(`Order ${order.orderNumber}: Customer city is required`);
  } else if (order.city.length > 50) {
    errors.push(`Order ${order.orderNumber}: Customer city too long (max 50 characters)`);
  }

  if (order.state && order.state.length > 50) {
    errors.push(`Order ${order.orderNumber}: Customer state too long (max 50 characters)`);
  }

  if (!order.zipCode.trim()) {
    errors.push(`Order ${order.orderNumber}: Customer zip code is required`);
  } else if (order.zipCode.length > 20) {
    errors.push(`Order ${order.orderNumber}: Customer zip code too long (max 20 characters)`);
  }

  if (!order.country.trim()) {
    errors.push(`Order ${order.orderNumber}: Customer country is required`);
  } else if (order.country.length > 50) {
    errors.push(`Order ${order.orderNumber}: Customer country too long (max 50 characters)`);
  }

  // Validate date
  try {
    const parsedDate = parseDate(order.date);
    if (isNaN(parsedDate.getTime())) {
      errors.push(`Order ${order.orderNumber}: Invalid date format`);
    }
  } catch (error) {
    errors.push(`Order ${order.orderNumber}: Invalid date format - ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Validate items
  if (order.items.length === 0) {
    errors.push(`Order ${order.orderNumber}: No items found`);
  }

  // Check for duplicate items
  const itemKeys = order.items.map(item => `${item.code}-${item.dose}`);
  const uniqueItemKeys = new Set(itemKeys);
  if (itemKeys.length !== uniqueItemKeys.size) {
    errors.push(`Order ${order.orderNumber}: Duplicate items found`);
  }

  // Validate product variants exist and get pricing
  const variants = await prisma.productVariant.findMany({
    where: {
      OR: order.items.map(item => ({
        code: item.code,
        dose: item.dose
      }))
    }
  });

  const variantMap = new Map(
    variants.map(v => [`${v.code}-${v.dose}`, v])
  );

  for (const item of order.items) {
    const variant = variantMap.get(`${item.code}-${item.dose}`);

    if (!variant) {
      errors.push(`Order ${order.orderNumber}: Product variant ${item.code} ${item.dose} not found in database`);
    }

    if (item.qty <= 0) {
      errors.push(`Order ${order.orderNumber}: Invalid quantity ${item.qty} for item ${item.code}`);
    }

    if (item.qty > 1000) {
      errors.push(`Order ${order.orderNumber}: Quantity ${item.qty} too high for item ${item.code} (max 1000)`);
    }
  }

  // Validate order totals make sense
  if (variants.length === order.items.length) {
    const subtotal = order.items.reduce((sum, item) => {
      const variant = variantMap.get(`${item.code}-${item.dose}`);
      return sum + (variant ? Number(variant.sellingPrice) * item.qty : 0);
    }, 0);

    if (subtotal <= 0) {
      errors.push(`Order ${order.orderNumber}: Invalid order total (subtotal: $${subtotal})`);
    }

    if (subtotal > 10000) {
      errors.push(`Order ${order.orderNumber}: Order total too high ($${subtotal}) - please verify`);
    }
  }

  return errors;
}

async function createOrder(order: GroupedOrder, dryRun: boolean = true): Promise<{ success: boolean; orderId?: string; error?: string }> {
  try {
    // Get product variants for pricing
    const variants = await prisma.productVariant.findMany({
      where: {
        OR: order.items.map(item => ({
          code: item.code,
          dose: item.dose
        }))
      }
    });

    const variantMap = new Map(
      variants.map(v => [`${v.code}-${v.dose}`, v])
    );

    // Parse the date
    const placedAt = parseDate(order.date);

    // Build order items with pricing
    const orderItems = order.items.map(item => {
      const variant = variantMap.get(`${item.code}-${item.dose}`);
      if (!variant) {
        throw new Error(`Variant not found: ${item.code} ${item.dose}`);
      }

      return {
        code: item.code,
        dose: item.dose,
        qty: item.qty,
        buyingPrice: Number(variant.buyingPrice),
        sellingPrice: Number(variant.sellingPrice),
        variantId: variant.id
      };
    });

    if (dryRun) {
      console.log(`  🔍 DRY RUN: Would create order ${order.orderNumber}`);
      console.log(`    Customer: ${order.customerName}`);
      console.log(`    Items: ${orderItems.length}`);
      console.log(`    Location: ${order.city}, ${order.state}, ${order.country}`);
      console.log(`    Date: ${placedAt.toISOString()}`);

      // Calculate totals for validation
      const subtotal = orderItems.reduce((sum, item) => sum + (Number(item.sellingPrice) * item.qty), 0);
      const shipping = 40;
      const discountRate = getDiscountRate(order.orderNumber);
      const discount = subtotal * discountRate;
      const totalUsd = subtotal + shipping - discount;

      console.log(`    Subtotal: $${subtotal.toFixed(2)}`);
      console.log(`    Shipping: $${shipping.toFixed(2)}`);
      console.log(`    Discount (${(discountRate * 100).toFixed(0)}%): $${discount.toFixed(2)}`);
      console.log(`    Total: $${totalUsd.toFixed(2)}`);

      return { success: true };
    }

    // Actually create the order using database transaction
    console.log(`  ✅ Creating order ${order.orderNumber}...`);

    // Calculate order totals
    const subtotal = orderItems.reduce((sum, item) => sum + (Number(item.sellingPrice) * item.qty), 0);
    const totalBuyingPrice = orderItems.reduce((sum, item) => sum + (Number(item.buyingPrice) * item.qty), 0);
    const shipping = 40;
    const discountRate = getDiscountRate(order.orderNumber);
    const discount = subtotal * discountRate;
    const totalUsd = subtotal + shipping - discount;

    // Calculate profit
    const profitUsd = subtotal - totalBuyingPrice;
    const profitInr = profitUsd * 86; // USD to INR conversion
    const profitMargin = (profitUsd / subtotal) * 100;

    // For BTC calculation, we'll use a default rate since we don't have the CoinGecko service here
    const btcRate = 100000; // Default BTC rate
    const totalBtc = totalUsd / btcRate;

    const createdOrder = await prisma.order.create({
      data: {
        orderNumber: order.orderNumber,
        placedAt,
        customerName: order.customerName,
        street1: order.address,
        city: order.city,
        state: order.state || null,
        postalCode: order.zipCode,
        country: order.country,
        totalUsd,
        totalBtc,
        profitUsd,
        profitInr,
        profitMargin,
        paymentMethod: 'BTC', // Default, will be left empty as requested
        paymentUrl: null,
        status: 'pending',
        items: {
          create: orderItems.map(item => ({
            code: item.code,
            dose: item.dose,
            qty: item.qty,
            buyingPrice: item.buyingPrice,
            sellingPrice: item.sellingPrice,
            variantId: item.variantId
          }))
        }
      }
    });

    console.log(`    ✅ Created order ${order.orderNumber} with ID: ${createdOrder.id}`);
    return { success: true, orderId: createdOrder.id };

  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

// Helper function to determine discount rate based on order number
function getDiscountRate(orderNumber: string): number {
  // Extract the order sequence number from format ORD-YYYY-####
  const match = orderNumber.match(/^ORD-\d{4}-(\d{4})$/);
  if (!match) return 0.05; // Invalid format, default to 5%

  const orderNum = parseInt(match[1]);

  // Orders 1-35 get 3% discount, 36+ get 5% discount
  return orderNum <= 35 ? 0.03 : 0.05;
}

async function importOrders(dryRun: boolean = true): Promise<ImportResult> {
  console.log(`🚀 Starting order import (${dryRun ? 'DRY RUN' : 'LIVE RUN'})...\n`);
  
  const result: ImportResult = {
    success: true,
    ordersProcessed: 0,
    ordersCreated: 0,
    ordersSkipped: 0,
    errors: [],
    createdOrderIds: []
  };
  
  try {
    // Parse CSV
    const orders = await parseCSV();
    result.ordersProcessed = orders.length;
    
    console.log(`\n🔍 Validating ${orders.length} orders...\n`);
    
    // Validate all orders first
    const validationResults = new Map<string, string[]>();
    
    for (const order of orders) {
      console.log(`Validating ${order.orderNumber}...`);
      const errors = await validateOrder(order);
      validationResults.set(order.orderNumber, errors);
      
      if (errors.length > 0) {
        console.log(`  ❌ Validation failed:`);
        errors.forEach(error => console.log(`    - ${error}`));
        result.errors.push(...errors);
      } else {
        console.log(`  ✅ Validation passed`);
      }
    }
    
    // Count validation failures
    const validOrders = orders.filter(order => 
      validationResults.get(order.orderNumber)?.length === 0
    );
    
    result.ordersSkipped = orders.length - validOrders.length;
    
    if (result.errors.length > 0) {
      console.log(`\n⚠️  Found ${result.errors.length} validation errors. Skipping ${result.ordersSkipped} orders.`);
      if (!dryRun) {
        console.log('❌ Aborting import due to validation errors.');
        result.success = false;
        return result;
      }
    }
    
    console.log(`\n📝 ${dryRun ? 'Simulating' : 'Creating'} ${validOrders.length} orders...\n`);
    
    // Create orders
    for (const order of validOrders) {
      const createResult = await createOrder(order, dryRun);
      
      if (createResult.success) {
        result.ordersCreated++;
        if (createResult.orderId) {
          result.createdOrderIds.push(createResult.orderId);
        }
      } else {
        result.errors.push(`Failed to create ${order.orderNumber}: ${createResult.error}`);
        console.log(`  ❌ Failed to create ${order.orderNumber}: ${createResult.error}`);
      }
    }
    
    return result;
    
  } catch (error) {
    result.success = false;
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    return result;
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const dryRun = !args.includes('--live');
  
  if (!dryRun) {
    console.log('⚠️  LIVE RUN MODE - This will actually create orders in the database!');
    console.log('Press Ctrl+C to cancel, or wait 5 seconds to continue...\n');
    await new Promise(resolve => setTimeout(resolve, 5000));
  }
  
  try {
    const result = await importOrders(dryRun);
    
    console.log('\n📊 IMPORT SUMMARY');
    console.log('='.repeat(50));
    console.log(`Mode: ${dryRun ? 'DRY RUN' : 'LIVE RUN'}`);
    console.log(`Orders processed: ${result.ordersProcessed}`);
    console.log(`Orders created: ${result.ordersCreated}`);
    console.log(`Orders skipped: ${result.ordersSkipped}`);
    console.log(`Errors: ${result.errors.length}`);
    
    if (result.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      result.errors.forEach(error => console.log(`  - ${error}`));
    }
    
    if (result.success) {
      console.log(`\n✅ Import ${dryRun ? 'simulation' : 'completed'} successfully!`);
      if (!dryRun && result.createdOrderIds.length > 0) {
        console.log(`Created order IDs: ${result.createdOrderIds.join(', ')}`);

        // Save rollback log
        const orderNumbers = validOrders.slice(0, result.ordersCreated).map(o => o.orderNumber);
        await saveRollbackLog(result.createdOrderIds, orderNumbers);
        console.log('\n💾 Rollback log saved. You can undo this import using:');
        console.log('npx tsx src/scripts/rollback-lost-orders.ts rollback');
      }
    } else {
      console.log('\n❌ Import failed!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Import failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { importOrders, parseCSV, validateOrder };
