import { promises as fs } from 'fs';
import path from 'path';
import { PrismaClient } from '../generated/prisma/index.js';

const prisma = new PrismaClient();

interface RollbackLog {
  timestamp: string;
  importedOrders: string[];
  totalOrders: number;
  description: string;
}

interface RollbackResult {
  success: boolean;
  ordersDeleted: number;
  errors: string[];
  deletedOrderNumbers: string[];
}

async function saveRollbackLog(orderIds: string[], orderNumbers: string[]): Promise<void> {
  const logEntry: RollbackLog = {
    timestamp: new Date().toISOString(),
    importedOrders: orderIds,
    totalOrders: orderIds.length,
    description: `Lost orders import: ${orderNumbers.join(', ')}`
  };
  
  const logPath = path.join(process.cwd(), 'src', 'scripts', 'rollback-logs');
  
  // Ensure directory exists
  try {
    await fs.mkdir(logPath, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }
  
  const logFile = path.join(logPath, `import-${Date.now()}.json`);
  await fs.writeFile(logFile, JSON.stringify(logEntry, null, 2));
  
  console.log(`📝 Rollback log saved to: ${logFile}`);
}

async function loadRollbackLogs(): Promise<RollbackLog[]> {
  const logPath = path.join(process.cwd(), 'src', 'scripts', 'rollback-logs');
  
  try {
    const files = await fs.readdir(logPath);
    const logFiles = files.filter(file => file.startsWith('import-') && file.endsWith('.json'));
    
    const logs: RollbackLog[] = [];
    
    for (const file of logFiles) {
      try {
        const content = await fs.readFile(path.join(logPath, file), 'utf-8');
        const log = JSON.parse(content) as RollbackLog;
        logs.push(log);
      } catch (error) {
        console.warn(`⚠️  Failed to read log file ${file}:`, error);
      }
    }
    
    return logs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  } catch (error) {
    console.log('📁 No rollback logs directory found');
    return [];
  }
}

async function rollbackImport(logIndex?: number): Promise<RollbackResult> {
  console.log('🔄 Starting rollback process...\n');
  
  const result: RollbackResult = {
    success: true,
    ordersDeleted: 0,
    errors: [],
    deletedOrderNumbers: []
  };
  
  try {
    const logs = await loadRollbackLogs();
    
    if (logs.length === 0) {
      result.success = false;
      result.errors.push('No import logs found');
      return result;
    }
    
    // Show available logs
    console.log('📋 Available import logs:');
    logs.forEach((log, index) => {
      const date = new Date(log.timestamp).toLocaleString();
      console.log(`  ${index + 1}. ${date} - ${log.totalOrders} orders - ${log.description}`);
    });
    
    // Select log to rollback
    let selectedLog: RollbackLog;
    
    if (logIndex !== undefined) {
      if (logIndex < 1 || logIndex > logs.length) {
        result.success = false;
        result.errors.push(`Invalid log index ${logIndex}. Available: 1-${logs.length}`);
        return result;
      }
      selectedLog = logs[logIndex - 1];
    } else {
      // Default to most recent
      selectedLog = logs[0];
      console.log(`\n🎯 Rolling back most recent import (${selectedLog.totalOrders} orders)`);
    }
    
    console.log(`\n🔍 Rolling back import from ${new Date(selectedLog.timestamp).toLocaleString()}`);
    console.log(`📦 Orders to delete: ${selectedLog.totalOrders}`);
    
    // Verify orders exist and get their order numbers
    const ordersToDelete = await prisma.order.findMany({
      where: {
        id: { in: selectedLog.importedOrders }
      },
      select: {
        id: true,
        orderNumber: true,
        customerName: true,
        totalUsd: true
      }
    });
    
    if (ordersToDelete.length === 0) {
      result.success = false;
      result.errors.push('No orders found to delete (they may have already been deleted)');
      return result;
    }
    
    console.log('\n📋 Orders to be deleted:');
    ordersToDelete.forEach(order => {
      console.log(`  - ${order.orderNumber} (${order.customerName}) - $${Number(order.totalUsd).toFixed(2)}`);
    });
    
    // Confirm deletion
    console.log(`\n⚠️  This will permanently delete ${ordersToDelete.length} orders and all their items.`);
    console.log('This action cannot be undone!');
    
    // In a real scenario, you might want to add a confirmation prompt here
    // For now, we'll proceed with the deletion
    
    console.log('\n🗑️  Deleting orders...');
    
    // Delete orders (items will be cascade deleted)
    for (const order of ordersToDelete) {
      try {
        await prisma.order.delete({
          where: { id: order.id }
        });
        
        result.ordersDeleted++;
        result.deletedOrderNumbers.push(order.orderNumber);
        console.log(`  ✅ Deleted ${order.orderNumber}`);
        
      } catch (error) {
        const errorMsg = `Failed to delete ${order.orderNumber}: ${error instanceof Error ? error.message : 'Unknown error'}`;
        result.errors.push(errorMsg);
        console.log(`  ❌ ${errorMsg}`);
      }
    }
    
    if (result.errors.length > 0) {
      result.success = false;
    }
    
    return result;
    
  } catch (error) {
    result.success = false;
    result.errors.push(error instanceof Error ? error.message : 'Unknown error');
    return result;
  }
}

async function listImports(): Promise<void> {
  console.log('📋 Import History\n');
  
  const logs = await loadRollbackLogs();
  
  if (logs.length === 0) {
    console.log('No import logs found.');
    return;
  }
  
  logs.forEach((log, index) => {
    const date = new Date(log.timestamp).toLocaleString();
    console.log(`${index + 1}. ${date}`);
    console.log(`   📦 Orders: ${log.totalOrders}`);
    console.log(`   📝 Description: ${log.description}`);
    console.log(`   🆔 Order IDs: ${log.importedOrders.slice(0, 3).join(', ')}${log.importedOrders.length > 3 ? '...' : ''}`);
    console.log();
  });
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    if (command === 'list') {
      await listImports();
    } else if (command === 'rollback') {
      const logIndex = args[1] ? parseInt(args[1]) : undefined;
      
      if (args[1] && isNaN(logIndex!)) {
        console.error('❌ Invalid log index. Use a number or omit to rollback the most recent import.');
        process.exit(1);
      }
      
      const result = await rollbackImport(logIndex);
      
      console.log('\n📊 ROLLBACK SUMMARY');
      console.log('='.repeat(50));
      console.log(`Orders deleted: ${result.ordersDeleted}`);
      console.log(`Errors: ${result.errors.length}`);
      
      if (result.deletedOrderNumbers.length > 0) {
        console.log(`\nDeleted orders: ${result.deletedOrderNumbers.join(', ')}`);
      }
      
      if (result.errors.length > 0) {
        console.log('\n❌ Errors encountered:');
        result.errors.forEach(error => console.log(`  - ${error}`));
      }
      
      if (result.success) {
        console.log('\n✅ Rollback completed successfully!');
      } else {
        console.log('\n❌ Rollback completed with errors!');
        process.exit(1);
      }
    } else {
      console.log('Usage:');
      console.log('  npx tsx src/scripts/rollback-lost-orders.ts list');
      console.log('  npx tsx src/scripts/rollback-lost-orders.ts rollback [log-index]');
      console.log('');
      console.log('Examples:');
      console.log('  npx tsx src/scripts/rollback-lost-orders.ts list                # List all imports');
      console.log('  npx tsx src/scripts/rollback-lost-orders.ts rollback           # Rollback most recent');
      console.log('  npx tsx src/scripts/rollback-lost-orders.ts rollback 2         # Rollback specific import');
    }
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (import.meta.url === `file://${process.argv[1]}`) {
  main();
}

export { saveRollbackLog, rollbackImport, listImports };
